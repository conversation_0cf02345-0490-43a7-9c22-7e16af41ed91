import openai
import os
from typing import Optional, Dict, Any, List


class OpenAIClient:
    """
    自定义 OpenAI 客户端，支持自定义 base_url 和 api_key
    """

    def __init__(self, api_key: str, base_url: Optional[str] = None):
        """
        初始化 OpenAI 客户端

        Args:
            api_key: OpenAI API 密钥
            base_url: 自定义 API 基础 URL，如果为 None 则使用默认 URL
        """
        self.api_key = api_key
        self.base_url = base_url or "https://api.openai.com/v1"

        # 初始化 OpenAI 客户端
        self.client = openai.OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )

    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        调用聊天完成 API

        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "消息内容"}]
            model: 使用的模型名称
            temperature: 温度参数，控制输出的随机性
            max_tokens: 最大 token 数量
            **kwargs: 其他参数

        Returns:
            API 响应结果
        """
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            return {
                "success": True,
                "data": response,
                "content": response.choices[0].message.content,
                "usage": response.usage
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "data": None
            }

    def simple_chat(self, prompt: str, model: str = "gpt-3.5-turbo", **kwargs) -> str:
        """
        简单的聊天接口

        Args:
            prompt: 用户输入的提示
            model: 使用的模型名称
            **kwargs: 其他参数

        Returns:
            模型的回复内容
        """
        messages = [{"role": "user", "content": prompt}]
        result = self.chat_completion(messages, model=model, **kwargs)

        if result["success"]:
            return result["content"]
        else:
            return f"错误: {result['error']}"


def interactive_chat(client: OpenAIClient, model: str = "gpt-3.5-turbo"):
    """
    交互式聊天函数

    Args:
        client: OpenAI 客户端实例
        model: 使用的模型名称
    """
    print("=" * 60)
    print("🤖 OpenAI 交互式聊天")
    print("=" * 60)
    print(f"使用模型: {model}")
    print("输入 'quit', 'exit', 'q' 退出")
    print("输入 'clear' 清空对话历史")
    print("输入 'help' 查看帮助")
    print("=" * 60)

    # 对话历史
    conversation_history = [
        {"role": "system", "content": "你是一个有用、友好的AI助手。请用中文回答问题。"}
    ]

    while True:
        try:
            # 获取用户输入
            user_input = input("\n👤 您: ").strip()

            # 处理特殊命令
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("\n👋 再见！感谢使用！")
                break
            elif user_input.lower() == 'clear':
                conversation_history = [
                    {"role": "system", "content": "你是一个有用、友好的AI助手。请用中文回答问题。"}
                ]
                print("\n🧹 对话历史已清空")
                continue
            elif user_input.lower() == 'help':
                print("\n📖 帮助信息:")
                print("  - 直接输入消息与AI对话")
                print("  - 'quit', 'exit', 'q': 退出程序")
                print("  - 'clear': 清空对话历史")
                print("  - 'help': 显示此帮助信息")
                continue
            elif not user_input:
                print("⚠️  请输入有效的消息")
                continue

            # 添加用户消息到历史
            conversation_history.append({"role": "user", "content": user_input})

            # 显示思考状态
            print("\n🤔 AI正在思考...")

            # 调用API
            result = client.chat_completion(
                messages=conversation_history,
                model=model,
                temperature=0.7,
                max_tokens=2000
            )

            if result["success"]:
                ai_response = result["content"]
                # 添加AI回复到历史
                conversation_history.append({"role": "assistant", "content": ai_response})

                # 显示AI回复
                print(f"\n🤖 AI: {ai_response}")

                # 显示token使用情况
                if result["usage"]:
                    usage = result["usage"]
                    print(f"\n📊 Token使用: 输入={usage.prompt_tokens}, 输出={usage.completion_tokens}, 总计={usage.total_tokens}")
            else:
                print(f"\n❌ 错误: {result['error']}")
                # 如果出错，移除最后添加的用户消息
                conversation_history.pop()

        except KeyboardInterrupt:
            print("\n\n👋 程序被中断，再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生未知错误: {e}")


def main():
    """
    主函数 - 启动交互式聊天
    """
    # 配置参数
    API_KEY = "sk-skad"  # 替换为您的 API key
    BASE_URL = "https://xyapi.mechat.top/v1"  # 替换为您的自定义 base URL
    MODEL = "gpt-4-1-mini"  # 默认使用的模型

    # 也可以从环境变量读取
    API_KEY = os.getenv("OPENAI_API_KEY", API_KEY)
    BASE_URL = os.getenv("OPENAI_BASE_URL", BASE_URL)
    MODEL = os.getenv("OPENAI_MODEL", MODEL)

    # 验证配置
    if API_KEY == "your-api-key-here":
        print("⚠️  警告: 请设置正确的 API_KEY")
        print("您可以:")
        print("1. 直接修改代码中的 API_KEY 变量")
        print("2. 设置环境变量 OPENAI_API_KEY")

        # 尝试从用户输入获取
        api_key_input = input("\n请输入您的 OpenAI API Key (或按回车跳过): ").strip()
        if api_key_input:
            API_KEY = api_key_input
        else:
            print("❌ 没有有效的 API Key，程序退出")
            return

    try:
        # 创建客户端实例
        print(f"\n🔧 正在初始化客户端...")
        print(f"   Base URL: {BASE_URL}")
        print(f"   Model: {MODEL}")

        client = OpenAIClient(api_key=API_KEY, base_url=BASE_URL)

        # 启动交互式聊天
        interactive_chat(client, model=MODEL)

    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        print("请检查您的 API Key 和网络连接")


if __name__ == "__main__":
    main()