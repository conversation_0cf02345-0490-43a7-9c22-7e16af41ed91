import openai
import os
from typing import Optional, Dict, Any, List


class OpenAIClient:
    """
    自定义 OpenAI 客户端，支持自定义 base_url 和 api_key
    """

    def __init__(self, api_key: str, base_url: Optional[str] = None):
        """
        初始化 OpenAI 客户端

        Args:
            api_key: OpenAI API 密钥
            base_url: 自定义 API 基础 URL，如果为 None 则使用默认 URL
        """
        self.api_key = api_key
        self.base_url = base_url or "https://api.openai.com/v1"

        # 初始化 OpenAI 客户端
        self.client = openai.OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )

    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        调用聊天完成 API

        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "消息内容"}]
            model: 使用的模型名称
            temperature: 温度参数，控制输出的随机性
            max_tokens: 最大 token 数量
            **kwargs: 其他参数

        Returns:
            API 响应结果
        """
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            return {
                "success": True,
                "data": response,
                "content": response.choices[0].message.content,
                "usage": response.usage
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "data": None
            }

    def simple_chat(self, prompt: str, model: str = "gpt-3.5-turbo", **kwargs) -> str:
        """
        简单的聊天接口

        Args:
            prompt: 用户输入的提示
            model: 使用的模型名称
            **kwargs: 其他参数

        Returns:
            模型的回复内容
        """
        messages = [{"role": "user", "content": prompt}]
        result = self.chat_completion(messages, model=model, **kwargs)

        if result["success"]:
            return result["content"]
        else:
            return f"错误: {result['error']}"


def main():
    """
    主函数 - 演示如何使用自定义 OpenAI 客户端
    """
    # 配置参数
    API_KEY = "your-api-key-here"  # 替换为您的 API key
    BASE_URL = "https://api.openai.com/v1"  # 替换为您的自定义 base URL

    # 也可以从环境变量读取
    # API_KEY = os.getenv("OPENAI_API_KEY", "your-api-key-here")
    # BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

    # 创建客户端实例
    client = OpenAIClient(api_key=API_KEY, base_url=BASE_URL)

    # 示例1: 简单聊天
    print("=== 简单聊天示例 ===")
    prompt = "你好，请介绍一下你自己"
    response = client.simple_chat(prompt)
    print(f"用户: {prompt}")
    print(f"AI: {response}")
    print()

    # 示例2: 多轮对话
    print("=== 多轮对话示例 ===")
    messages = [
        {"role": "system", "content": "你是一个有用的助手"},
        {"role": "user", "content": "什么是人工智能？"},
        {"role": "assistant", "content": "人工智能（AI）是计算机科学的一个分支..."},
        {"role": "user", "content": "AI有哪些应用领域？"}
    ]

    result = client.chat_completion(
        messages=messages,
        model="gpt-3.5-turbo",
        temperature=0.7,
        max_tokens=500
    )

    if result["success"]:
        print("对话成功:")
        print(f"回复: {result['content']}")
        print(f"使用的 tokens: {result['usage']}")
    else:
        print(f"对话失败: {result['error']}")

    print()

    # 示例3: 使用不同模型
    print("=== 不同模型示例 ===")
    models_to_test = ["gpt-3.5-turbo", "gpt-4"]  # 根据您的可用模型调整

    for model in models_to_test:
        print(f"测试模型: {model}")
        response = client.simple_chat(
            "用一句话解释量子计算",
            model=model,
            temperature=0.5
        )
        print(f"回复: {response}")
        print()


if __name__ == "__main__":
    main()