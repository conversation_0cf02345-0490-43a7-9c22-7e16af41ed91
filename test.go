package main

import (
	"bufio"
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/websocket"
)

// 定义常量
const (
	websocketURI = "wss://matrix.tencent.com/ai_gen_txt_server/getClassify"
	captchaAPI   = "https://t.captcha.qq.com/cap_union_new_verify"

	// 默认凭证
	defaultTicket  = "t03I-LEsLWnsGBT3OKL4Q0bOjGfwOc-cB9vRbE07iWvRIPmzYZuuaXVOi_h-L6QFNaxcZnEBnp9sem3g6tMcx1ah_VUKxC1EhnalM9rMvUxuyiYxNjy27SoIcEQZmWPe0PNiqaS3mW3TVQ4uhtdpKectW8FkJqcOtwYTsYVZBD6kuPsjPRfzxB77Q**"
	defaultRandstr = "@HyN"
)

// BrowserConfig 浏览器配置结构
type BrowserConfig struct {
	UserAgent     string
	SecChUa       string
	Platform      string
	Fingerprint   string
	Cookies       string
	ChromeVersion int
	WebKitVersion string
}

// 预定义的浏览器配置
var browserConfigs = []BrowserConfig{
	{
		ChromeVersion: 131,
		WebKitVersion: "537.36",
		Platform:      "Windows",
		Fingerprint:   "b8c9d4e5f6a7b8c9d0e1f2a3b4c5d6e7",
	},
	{
		ChromeVersion: 130,
		WebKitVersion: "537.36",
		Platform:      "Windows",
		Fingerprint:   "c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4",
	},
	{
		ChromeVersion: 129,
		WebKitVersion: "537.36",
		Platform:      "Windows",
		Fingerprint:   "d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5",
	},
	{
		ChromeVersion: 128,
		WebKitVersion: "537.36",
		Platform:      "macOS",
		Fingerprint:   "e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6",
	},
	{
		ChromeVersion: 127,
		WebKitVersion: "537.36",
		Platform:      "Linux",
		Fingerprint:   "f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7",
	},
}

// ============ 数据结构定义 ============

// CaptchaInitResponse 定义验证码初始化响应结构
type CaptchaInitResponse struct {
	State   int    `json:"state"`
	Ticket  string `json:"ticket"`
	Sess    string `json:"sess"`
	Randstr string `json:"randstr"`
	Data    struct {
		CommCaptchaCfg struct {
			PowCfg struct {
				Prefix string `json:"prefix"`
				Md5    string `json:"md5"`
			} `json:"pow_cfg"`
		} `json:"comm_captcha_cfg"`
	} `json:"data"`
}

// CaptchaVerifyResponse 定义验证码验证响应结构
type CaptchaVerifyResponse struct {
	ErrorCode  string `json:"errorCode"`
	Ticket     string `json:"ticket"`
	Randstr    string `json:"randstr"`
	ErrMessage string `json:"errMessage"`
}

// Credentials 验证凭证结构
type Credentials struct {
	Ticket  string
	Randstr string
}

// TextAnalysisClient WebSocket客户端结构
type TextAnalysisClient struct {
	conn          *websocket.Conn
	isConnected   bool
	credentials   *Credentials
	sessionLog    []SessionMessage // 会话日志
	browserConfig *BrowserConfig   // 浏览器配置
}

// DynamicParams 动态参数结构
type DynamicParams struct {
	Collect string
	Eks     string
	VData   string
}

// SessionMessage 会话消息结构
type SessionMessage struct {
	Timestamp string `json:"timestamp"`
	Direction string `json:"direction"` // "SEND" 或 "RECV"
	Message   string `json:"message"`
	Type      string `json:"type"` // "fingerprint", "credentials", "text", "response"
}

// ============ 工具函数 ============

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// generateRandomBrowserConfig 生成随机浏览器配置
func generateRandomBrowserConfig() *BrowserConfig {
	// 初始化随机种子
	rand.Seed(time.Now().UnixNano())

	// 随机选择一个基础配置
	baseConfig := browserConfigs[rand.Intn(len(browserConfigs))]

	// 生成完整的配置
	config := &BrowserConfig{
		ChromeVersion: baseConfig.ChromeVersion,
		WebKitVersion: baseConfig.WebKitVersion,
		Platform:      baseConfig.Platform,
		Fingerprint:   baseConfig.Fingerprint,
	}

	// 根据平台生成User-Agent
	switch config.Platform {
	case "Windows":
		config.UserAgent = fmt.Sprintf("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/%s (KHTML, like Gecko) Chrome/%d.0.0.0 Safari/%s",
			config.WebKitVersion, config.ChromeVersion, config.WebKitVersion)
		config.SecChUa = fmt.Sprintf(`"Google Chrome";v="%d", "Chromium";v="%d", "Not)A;Brand";v="8"`,
			config.ChromeVersion, config.ChromeVersion)
	case "macOS":
		config.UserAgent = fmt.Sprintf("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/%s (KHTML, like Gecko) Chrome/%d.0.0.0 Safari/%s",
			config.WebKitVersion, config.ChromeVersion, config.WebKitVersion)
		config.SecChUa = fmt.Sprintf(`"Google Chrome";v="%d", "Chromium";v="%d", "Not)A;Brand";v="8"`,
			config.ChromeVersion, config.ChromeVersion)
	case "Linux":
		config.UserAgent = fmt.Sprintf("Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/%s (KHTML, like Gecko) Chrome/%d.0.0.0 Safari/%s",
			config.WebKitVersion, config.ChromeVersion, config.WebKitVersion)
		config.SecChUa = fmt.Sprintf(`"Google Chrome";v="%d", "Chromium";v="%d", "Not)A;Brand";v="8"`,
			config.ChromeVersion, config.ChromeVersion)
	}

	// 生成随机Cookie
	config.Cookies = generateRandomCookies()

	return config
}

// generateRandomCookies 生成随机Cookie
func generateRandomCookies() string {
	// 生成随机的设备ID和会话ID
	deviceId := generateRandomHex(32)
	sessionId := generateRandomHex(16)
	timestamp := time.Now().Unix()

	return fmt.Sprintf("_ga=GA1.2.%d.%d; _gcl_au=1.1.%d.%d; device_id=%s; session_id=%s",
		rand.Intn(999999999)+100000000, timestamp-86400,
		rand.Intn(999999999)+100000000, timestamp,
		deviceId, sessionId)
}

// generateRandomHex 生成指定长度的随机十六进制字符串
func generateRandomHex(length int) string {
	chars := "0123456789abcdef"
	result := make([]byte, length)
	for i := range result {
		result[i] = chars[rand.Intn(len(chars))]
	}
	return string(result)
}

// generateDynamicParams 生成动态参数
func generateDynamicParams(browserConfig *BrowserConfig) *DynamicParams {
	fmt.Printf("🔧 [PARAMS] 生成动态参数...\n")

	// 生成动态collect参数
	collect := generateDynamicCollect(browserConfig)

	// 生成动态eks参数
	eks := generateDynamicEks()

	// 生成vData参数
	vData := generateDynamicVData()

	fmt.Printf("   [INFO] 动态collect长度: %d字符\n", len(collect))
	fmt.Printf("   [INFO] 动态eks长度: %d字符\n", len(eks))
	fmt.Printf("   [INFO] 动态vData: %s\n", vData)

	return &DynamicParams{
		Collect: collect,
		Eks:     eks,
		VData:   vData,
	}
}

// generateDynamicCollect 生成动态collect参数 - 基于真实TDC.js逻辑
func generateDynamicCollect(browserConfig *BrowserConfig) string {
	fmt.Printf("   [TDC] 基于真实TDC.js逻辑生成collect参数...\n")

	// 1. 创建TDC环境模拟器
	tdcEnv := NewTDCEnvironment(browserConfig)

	// 2. 模拟window.TDC.getData()调用
	collectData := tdcEnv.GenerateCollectData()

	fmt.Printf("   [TDC] collect参数生成完成，长度: %d\n", len(collectData))
	return collectData
}

// collectBrowserEnvironment 收集浏览器环境信息
func collectBrowserEnvironment(browserConfig *BrowserConfig) string {
	// 模拟真实的浏览器环境数据收集
	screenWidth := 1920 + rand.Intn(480)  // 1920-2400
	screenHeight := 1080 + rand.Intn(360) // 1080-1440

	envInfo := map[string]interface{}{
		"userAgent":     browserConfig.UserAgent,
		"language":      "zh-CN",
		"languages":     []string{"zh-CN", "zh", "en"},
		"platform":      browserConfig.Platform,
		"cookieEnabled": true,
		"doNotTrack":    nil,
		"screen": map[string]interface{}{
			"width":       screenWidth,
			"height":      screenHeight,
			"availWidth":  screenWidth,
			"availHeight": screenHeight - 40, // 减去任务栏高度
			"colorDepth":  24,
			"pixelDepth":  24,
		},
		"timezone":       -480, // UTC+8
		"timezoneOffset": 480,
		"plugins":        []string{"Chrome PDF Plugin", "Chrome PDF Viewer", "Native Client"},
		"mimeTypes":      []string{"application/pdf", "application/x-google-chrome-pdf"},
	}

	// 序列化环境信息
	envStr := fmt.Sprintf("%+v", envInfo)
	return base64.StdEncoding.EncodeToString([]byte(envStr))
}

// generateCanvasFingerprint 生成Canvas指纹
func generateCanvasFingerprint() string {
	// 模拟Canvas指纹生成过程
	canvasText := "BrowserLeaks,com <canvas> 1.0"
	canvasData := fmt.Sprintf("canvas_fp_%s_%d", canvasText, time.Now().UnixNano())

	// 模拟Canvas渲染结果的哈希
	hash := md5.Sum([]byte(canvasData))
	return hex.EncodeToString(hash[:])[:16]
}

// generateWebGLFingerprint 生成WebGL指纹
func generateWebGLFingerprint() string {
	// 模拟WebGL指纹信息
	webglInfo := map[string]string{
		"vendor":     "Google Inc. (Intel)",
		"renderer":   "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)",
		"version":    "WebGL 1.0 (OpenGL ES 2.0 Chromium)",
		"extensions": "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_color_buffer_half_float",
	}

	webglStr := fmt.Sprintf("%+v", webglInfo)
	hash := md5.Sum([]byte(webglStr))
	return hex.EncodeToString(hash[:])[:16]
}

// encodeTDCData 模拟TDC.js的编码过程
func encodeTDCData(data string) string {
	// 第一层：Base64编码
	level1 := base64.StdEncoding.EncodeToString([]byte(data))

	// 第二层：添加时间戳和随机数
	timestamp := time.Now().Unix()
	random := rand.Int63()
	combined := fmt.Sprintf("%s_%d_%d", level1, timestamp, random)

	// 第三层：再次Base64编码
	level2 := base64.StdEncoding.EncodeToString([]byte(combined))

	// 第四层：URL编码（模拟最终传输格式）
	return url.QueryEscape(level2)
}

// generateDynamicEks 生成动态eks参数 - 基于TDC.js中的特定行
func generateDynamicEks() string {
	// 根据您的指导，eks参数来自tdc.js中的特定行
	// 这里我们需要模拟从tdc.js中提取的eks值

	// 模拟tdc.js中的eks生成逻辑
	timestamp := time.Now().Unix()
	random := rand.Int63()

	// 生成类似真实tdc.js中的eks格式
	eksData := fmt.Sprintf("tdc_eks_%d_%d", timestamp, random)

	// 使用特定的编码方式（模拟tdc.js的编码）
	hash := md5.Sum([]byte(eksData))
	hashStr := hex.EncodeToString(hash[:])

	// 组合成最终的eks格式
	finalEks := fmt.Sprintf("%s_%s", hashStr, generateRandomString(32))

	// Base64编码
	encoded := base64.StdEncoding.EncodeToString([]byte(finalEks))

	return encoded
}

// TDCEnvironment TDC.js环境模拟器
type TDCEnvironment struct {
	UserAgent string
	Platform  string
	Language  string
	Screen    ScreenData
	Canvas    CanvasData
	WebGL     WebGLData
	Plugins   []string
	MimeTypes []string
	Timezone  int
}

type ScreenData struct {
	Width       int
	Height      int
	AvailWidth  int
	AvailHeight int
	ColorDepth  int
	PixelDepth  int
}

type CanvasData struct {
	Fingerprint string
	TextMetrics string
}

type WebGLData struct {
	Vendor     string
	Renderer   string
	Version    string
	Extensions string
}

// NewTDCEnvironment 创建TDC环境模拟器
func NewTDCEnvironment(browserConfig *BrowserConfig) *TDCEnvironment {
	screenWidth := 1920 + rand.Intn(480)
	screenHeight := 1080 + rand.Intn(360)

	return &TDCEnvironment{
		UserAgent: browserConfig.UserAgent,
		Platform:  browserConfig.Platform,
		Language:  "zh-CN",
		Screen: ScreenData{
			Width:       screenWidth,
			Height:      screenHeight,
			AvailWidth:  screenWidth,
			AvailHeight: screenHeight - 40,
			ColorDepth:  24,
			PixelDepth:  24,
		},
		Canvas: CanvasData{
			Fingerprint: generateRealisticCanvasFingerprint(),
			TextMetrics: "12.34,56.78",
		},
		WebGL: WebGLData{
			Vendor:     "Google Inc. (Intel)",
			Renderer:   "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)",
			Version:    "WebGL 1.0 (OpenGL ES 2.0 Chromium)",
			Extensions: "ANGLE_instanced_arrays,EXT_blend_minmax,EXT_color_buffer_half_float,EXT_frag_depth",
		},
		Plugins:   []string{"Chrome PDF Plugin", "Chrome PDF Viewer", "Native Client"},
		MimeTypes: []string{"application/pdf", "application/x-google-chrome-pdf", "application/x-nacl"},
		Timezone:  -480, // UTC+8
	}
}

// generateRealisticCanvasFingerprint 生成真实的Canvas指纹
func generateRealisticCanvasFingerprint() string {
	// 模拟真实的Canvas指纹生成过程
	canvasText := "Cwm fjordbank glyphs vext quiz, 😃"
	canvasData := fmt.Sprintf("canvas_%s_%d", canvasText, time.Now().UnixNano()%1000000)

	// 生成类似真实Canvas指纹的哈希
	hash := md5.Sum([]byte(canvasData))
	return hex.EncodeToString(hash[:])
}

// GenerateCollectData 生成collect数据 - 模拟TDC.getData()
func (env *TDCEnvironment) GenerateCollectData() string {
	fmt.Printf("   [TDC] 模拟window.TDC.getData()生成collect...\n")

	// 1. 收集所有环境数据
	envData := env.collectAllEnvironmentData()

	// 2. 模拟TDC.js的数据处理逻辑
	processedData := env.processTDCData(envData)

	// 3. 最终编码
	finalData := env.encodeFinalData(processedData)

	fmt.Printf("   [TDC] collect数据生成完成\n")
	return finalData
}

// collectAllEnvironmentData 收集所有环境数据
func (env *TDCEnvironment) collectAllEnvironmentData() map[string]interface{} {
	return map[string]interface{}{
		"userAgent":      env.UserAgent,
		"platform":       env.Platform,
		"language":       env.Language,
		"languages":      []string{"zh-CN", "zh", "en"},
		"cookieEnabled":  true,
		"doNotTrack":     nil,
		"onLine":         true,
		"screen":         env.Screen,
		"canvas":         env.Canvas,
		"webgl":          env.WebGL,
		"plugins":        env.Plugins,
		"mimeTypes":      env.MimeTypes,
		"timezone":       env.Timezone,
		"timezoneOffset": -env.Timezone,
		"timestamp":      time.Now().Unix(),
		"random":         rand.Int63(),
	}
}

// processTDCData 处理TDC数据
func (env *TDCEnvironment) processTDCData(data map[string]interface{}) string {
	// 序列化数据
	dataStr := fmt.Sprintf("%+v", data)

	// 第一层处理
	level1 := base64.StdEncoding.EncodeToString([]byte(dataStr))

	// 添加校验和
	checksum := env.generateChecksum(level1)
	combined := fmt.Sprintf("%s|%s", level1, checksum)

	return combined
}

// generateChecksum 生成校验和
func (env *TDCEnvironment) generateChecksum(data string) string {
	hash := md5.Sum([]byte(data + env.UserAgent))
	return hex.EncodeToString(hash[:])[:16]
}

// encodeFinalData 最终编码
func (env *TDCEnvironment) encodeFinalData(data string) string {
	// 最终Base64编码
	encoded := base64.StdEncoding.EncodeToString([]byte(data))

	// URL编码
	return url.QueryEscape(encoded)
}

// generateDynamicVData 生成vData参数
func generateDynamicVData() string {
	// vData通常包含版本和验证信息
	version := "1.0.0"
	timestamp := time.Now().Unix()
	checksum := generateDynamicChecksum()

	vData := fmt.Sprintf("v=%s&t=%d&c=%s", version, timestamp, checksum)
	return base64.StdEncoding.EncodeToString([]byte(vData))
}

// generateDynamicChecksum 生成校验和
func generateDynamicChecksum() string {
	data := fmt.Sprintf("checksum_%d_%s", time.Now().UnixNano(), generateRandomString(8))
	hash := md5.Sum([]byte(data))
	return hex.EncodeToString(hash[:])[:16] // 取前16位
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	chars := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = chars[rand.Intn(len(chars))]
	}
	return string(result)
}

// MD5哈希计算
func calculateMD5(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

// POW算法计算
func calculatePOW(prefix, target string) (int, error) {
	for i := 0; i < 100000; i++ {
		testStr := prefix + strconv.Itoa(i)
		hash := calculateMD5(testStr)
		if hash == target {
			return i, nil
		}
	}
	return 0, fmt.Errorf("POW计算失败")
}

// ============ 验证码模块 ============

// 初始化验证码
func initCaptcha() (*CaptchaInitResponse, error) {
	// 使用正确的User-Agent（已经base64编码）
	userAgent := "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
	encodedUA := "TW96aWxsYS81LjAgKFdpbmRvd3MgTlQgMTAuMDsgV2luNjQ7IHg2NCkgQXBwbGVXZWJLaXQvNTM3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEzOC4wLjAuMCBTYWZhcmkvNTM3LjM2"

	// 构建初始化URL - 使用正确的参数
	initURL := fmt.Sprintf("https://t.captcha.qq.com/cap_union_prehandle?aid=2089775896&protocol=https&accver=1&showtype=popup&ua=%s&noheader=1&fb=1&aged=0&enableAged=0&enableDarkMode=0&grayscale=1&dyeid=0&clientype=2&cap_cd=&uid=&lang=zh-cn&entry_url=https%%3A%%2F%%2Fmatrix.tencent.com%%2Fai-detect%%2Fai_gen&elder_captcha=0&js=&login_appid=&wb=2&version=1.1.0&subsid=1&callback=_aq_203121&sess=", encodedUA)

	client := &http.Client{Timeout: 30 * time.Second}

	req, err := http.NewRequest("GET", initURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建初始化请求失败: %v", err)
	}

	// 设置正确的请求头
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("User-Agent", userAgent)
	req.Header.Set("Referer", "https://matrix.tencent.com/")
	req.Header.Set("Sec-Fetch-Dest", "script")
	req.Header.Set("Sec-Fetch-Mode", "no-cors")
	req.Header.Set("Sec-Fetch-Site", "cross-site")
	req.Header.Set("Sec-Fetch-Storage-Access", "active")
	req.Header.Set("sec-ch-ua", `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`)
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-platform", `"Windows"`)

	// 设置Cookie
	req.Header.Set("Cookie", "yyb_muid=303E8C8BB5F562161B2C9956B4936328; omgid=0_xFA9PHnBt3HS4")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送初始化请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取初始化响应失败: %v", err)
	}

	fmt.Printf("🔧 初始化响应: %s\n", string(body))

	// 解析JSONP响应
	bodyStr := string(body)
	start := strings.Index(bodyStr, "(") + 1
	end := strings.LastIndex(bodyStr, ")")
	if start <= 0 || end <= 0 || start >= end {
		return nil, fmt.Errorf("解析JSONP响应失败")
	}

	jsonStr := bodyStr[start:end]
	var initResp CaptchaInitResponse
	if err := json.Unmarshal([]byte(jsonStr), &initResp); err != nil {
		return nil, fmt.Errorf("解析初始化响应JSON失败: %v", err)
	}

	fmt.Printf("🔧 验证码初始化成功，sess: %s\n", initResp.Sess[:20]+"...")
	return &initResp, nil
}

// 获取验证码凭证
func getCaptchaCredentials(browserConfig *BrowserConfig) (*Credentials, error) {
	fmt.Printf("   [STEP-3.1] 开始初始化验证码...\n")

	// 1. 初始化验证码
	initResp, err := initCaptcha()
	if err != nil {
		fmt.Printf("   ⚠️  [WARN] 初始化验证码失败，使用默认凭证\n")
		fmt.Printf("   [WARN] 初始化失败原因: %v\n", err)
		fmt.Printf("   [WARN] 错误类型: %T\n", err)
		fmt.Printf("   [INFO] 使用默认凭证 - Ticket长度: %d, Randstr: %s\n", len(defaultTicket), defaultRandstr)
		return &Credentials{Ticket: defaultTicket, Randstr: defaultRandstr}, nil
	}

	fmt.Printf("   ✅ [STEP-3.1] 验证码初始化成功\n")
	fmt.Printf("   [INFO] Session ID: %s\n", initResp.Sess[:min(20, len(initResp.Sess))]+"...")
	fmt.Printf("   [INFO] State: %d\n", initResp.State)

	// 2. 计算POW
	fmt.Printf("   [STEP-3.2] 开始计算POW算法...\n")
	powAnswer := 0
	powCalcTime := 21

	if initResp.Data.CommCaptchaCfg.PowCfg.Prefix != "" && initResp.Data.CommCaptchaCfg.PowCfg.Md5 != "" {
		fmt.Printf("   [INFO] POW参数 - Prefix: %s\n", initResp.Data.CommCaptchaCfg.PowCfg.Prefix)
		fmt.Printf("   [INFO] POW参数 - Target MD5: %s\n", initResp.Data.CommCaptchaCfg.PowCfg.Md5)
		fmt.Printf("   [DEBUG] 开始POW计算...\n")

		startTime := time.Now()
		powAnswer, err = calculatePOW(initResp.Data.CommCaptchaCfg.PowCfg.Prefix, initResp.Data.CommCaptchaCfg.PowCfg.Md5)
		calcDuration := time.Since(startTime)

		if err != nil {
			fmt.Printf("   ⚠️  [WARN] POW计算失败，使用默认值\n")
			fmt.Printf("   [WARN] POW失败原因: %v\n", err)
			fmt.Printf("   [WARN] 计算耗时: %v\n", calcDuration)
			powAnswer = 808
		} else {
			fmt.Printf("   ✅ [STEP-3.2] POW计算成功\n")
			fmt.Printf("   [INFO] POW答案: %d\n", powAnswer)
			fmt.Printf("   [INFO] 计算耗时: %v\n", calcDuration)
		}
	} else {
		fmt.Printf("   [WARN] POW参数为空，跳过计算\n")
		fmt.Printf("   [DEBUG] Prefix: '%s', MD5: '%s'\n", initResp.Data.CommCaptchaCfg.PowCfg.Prefix, initResp.Data.CommCaptchaCfg.PowCfg.Md5)
	}

	// 3. 构建验证请求数据
	fmt.Printf("   [STEP-3.3] 构建验证请求数据...\n")

	// 生成动态参数
	dynamicParams := generateDynamicParams(browserConfig)

	data := url.Values{}
	data.Set("collect", dynamicParams.Collect)
	data.Set("tlg", "1024")
	data.Set("eks", dynamicParams.Eks)
	data.Set("sess", initResp.Sess)
	data.Set("ans", `[{"elem_id":0,"type":"DynAnswerType_TIME","data":""}]`)

	powAnswerStr := fmt.Sprintf("%s#%d", initResp.Data.CommCaptchaCfg.PowCfg.Prefix, powAnswer)
	data.Set("pow_answer", powAnswerStr)
	data.Set("pow_calc_time", strconv.Itoa(powCalcTime))

	fmt.Printf("   [INFO] 请求参数 - Session: %s\n", initResp.Sess[:min(20, len(initResp.Sess))]+"...")
	fmt.Printf("   [INFO] 请求参数 - POW Answer: %s\n", powAnswerStr)
	fmt.Printf("   [INFO] 请求参数 - POW Calc Time: %d\n", powCalcTime)
	fmt.Printf("   [INFO] 使用优化的collect和eks参数\n")
	fmt.Printf("   [DEBUG] 请求数据长度: %d字节\n", len(data.Encode()))

	// 4. 发送验证请求
	fmt.Printf("   [STEP-3.4] 发送验证请求到API...\n")
	fmt.Printf("   [INFO] API地址: %s\n", captchaAPI)

	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", captchaAPI, bytes.NewBufferString(data.Encode()))
	if err != nil {
		fmt.Printf("   ❌ [ERROR] 创建验证请求失败: %v\n", err)
		return nil, fmt.Errorf("[STEP-3.4] 创建验证请求失败: %v", err)
	}

	// 设置请求头 - 使用随机浏览器配置
	req.Header.Set("Accept", "application/json, text/javascript, */*; q=0.01")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
	req.Header.Set("Origin", "https://captcha.gtimg.com")   // 使用curl中的Origin
	req.Header.Set("Referer", "https://captcha.gtimg.com/") // 使用curl中的Referer
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "cross-site")
	req.Header.Set("User-Agent", browserConfig.UserAgent)
	req.Header.Set("sec-ch-ua", browserConfig.SecChUa)
	req.Header.Set("sec-ch-ua-mobile", "?0")
	req.Header.Set("sec-ch-ua-platform", fmt.Sprintf(`"%s"`, browserConfig.Platform))

	fmt.Printf("   [DEBUG] 请求头设置完成，开始发送请求...\n")
	requestStartTime := time.Now()

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("   ❌ [ERROR] 发送验证请求失败: %v\n", err)
		fmt.Printf("   [ERROR] 请求耗时: %v\n", time.Since(requestStartTime))
		return nil, fmt.Errorf("[STEP-3.4] 发送验证请求失败: %v", err)
	}
	defer resp.Body.Close()

	fmt.Printf("   [INFO] HTTP状态码: %d\n", resp.StatusCode)
	fmt.Printf("   [INFO] 请求耗时: %v\n", time.Since(requestStartTime))

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("   ❌ [ERROR] 读取验证响应失败: %v\n", err)
		return nil, fmt.Errorf("[STEP-3.4] 读取验证响应失败: %v", err)
	}

	fmt.Printf("   [INFO] API响应长度: %d字节\n", len(body))
	fmt.Printf("   [DEBUG] API完整响应: %s\n", string(body))

	// 解析验证响应
	fmt.Printf("   [STEP-3.5] 解析API响应...\n")
	var verifyResp CaptchaVerifyResponse
	if err := json.Unmarshal(body, &verifyResp); err != nil {
		fmt.Printf("   ❌ [ERROR] JSON解析失败: %v\n", err)
		fmt.Printf("   [ERROR] 原始响应: %s\n", string(body))
		return nil, fmt.Errorf("[STEP-3.5] 解析验证响应失败: %v", err)
	}

	fmt.Printf("   [INFO] 解析结果 - ErrorCode: '%s'\n", verifyResp.ErrorCode)
	fmt.Printf("   [INFO] 解析结果 - Ticket: '%s'\n", verifyResp.Ticket[:min(20, len(verifyResp.Ticket))]+"...")
	fmt.Printf("   [INFO] 解析结果 - Randstr: '%s'\n", verifyResp.Randstr)
	fmt.Printf("   [INFO] 解析结果 - ErrMessage: '%s'\n", verifyResp.ErrMessage)

	// 检查验证结果
	if verifyResp.ErrorCode != "0" && verifyResp.ErrorCode != "" {
		fmt.Printf("   ⚠️  [WARN] 验证失败，使用默认凭证\n")
		fmt.Printf("   [WARN] 错误码: %s\n", verifyResp.ErrorCode)
		fmt.Printf("   [WARN] 错误信息: %s\n", verifyResp.ErrMessage)
		fmt.Printf("   [WARN] 默认凭证 - Ticket长度: %d, Randstr: %s\n", len(defaultTicket), defaultRandstr)
		return &Credentials{Ticket: defaultTicket, Randstr: defaultRandstr}, nil
	}

	// 如果获取到的凭证为空，使用默认凭证
	if verifyResp.Ticket == "" || verifyResp.Randstr == "" {
		fmt.Printf("   ⚠️  [WARN] 获取到的凭证为空，使用默认凭证\n")
		fmt.Printf("   [WARN] 返回的Ticket长度: %d\n", len(verifyResp.Ticket))
		fmt.Printf("   [WARN] 返回的Randstr长度: %d\n", len(verifyResp.Randstr))
		fmt.Printf("   [WARN] 默认凭证 - Ticket长度: %d, Randstr: %s\n", len(defaultTicket), defaultRandstr)
		return &Credentials{Ticket: defaultTicket, Randstr: defaultRandstr}, nil
	}

	fmt.Printf("   ✅ [STEP-3.5] 成功获取有效凭证\n")
	fmt.Printf("   [INFO] 有效凭证 - Ticket长度: %d\n", len(verifyResp.Ticket))
	fmt.Printf("   [INFO] 有效凭证 - Randstr: %s\n", verifyResp.Randstr)
	return &Credentials{Ticket: verifyResp.Ticket, Randstr: verifyResp.Randstr}, nil
}

// ============ WebSocket客户端模块 ============

// NewTextAnalysisClient 创建新的文本分析客户端
func NewTextAnalysisClient() *TextAnalysisClient {
	// 生成随机浏览器配置
	browserConfig := generateRandomBrowserConfig()

	fmt.Printf("🎭 [BROWSER] 生成随机浏览器配置:\n")
	fmt.Printf("   [INFO] 平台: %s\n", browserConfig.Platform)
	fmt.Printf("   [INFO] Chrome版本: %d\n", browserConfig.ChromeVersion)
	fmt.Printf("   [INFO] 指纹: %s\n", browserConfig.Fingerprint)
	fmt.Printf("   [INFO] User-Agent: %s\n", browserConfig.UserAgent[:min(80, len(browserConfig.UserAgent))]+"...")

	return &TextAnalysisClient{
		isConnected:   false,
		credentials:   nil,
		sessionLog:    make([]SessionMessage, 0),
		browserConfig: browserConfig,
	}
}

// addSessionLog 添加会话日志
func (client *TextAnalysisClient) addSessionLog(direction, message, msgType string) {
	logEntry := SessionMessage{
		Timestamp: time.Now().Format("2006-01-02 15:04:05.000"),
		Direction: direction,
		Message:   message,
		Type:      msgType,
	}
	client.sessionLog = append(client.sessionLog, logEntry)
}

// printSessionLog 打印完整会话日志
func (client *TextAnalysisClient) printSessionLog() {
	fmt.Println("\n📋 [SESSION-LOG] 完整WebSocket会话记录:")
	fmt.Println(strings.Repeat("=", 80))

	for i, entry := range client.sessionLog {
		fmt.Printf("[%d] %s [%s-%s] %s\n",
			i+1, entry.Timestamp, entry.Direction, entry.Type, entry.Message)
	}

	fmt.Println(strings.Repeat("=", 80))
	fmt.Printf("📊 [SESSION-SUMMARY] 会话统计: 总计 %d 条消息\n", len(client.sessionLog))

	// 统计消息类型
	sendCount := 0
	recvCount := 0
	for _, entry := range client.sessionLog {
		if entry.Direction == "SEND" {
			sendCount++
		} else {
			recvCount++
		}
	}
	fmt.Printf("   📤 发送消息: %d 条\n", sendCount)
	fmt.Printf("   📥 接收消息: %d 条\n", recvCount)
}

// Connect 第一步：建立WebSocket连接
func (client *TextAnalysisClient) Connect() error {
	fmt.Println("🔗 [STEP-1] 开始建立WebSocket连接...")
	fmt.Printf("   [INFO] 目标地址: %s\n", websocketURI)
	fmt.Printf("   [DEBUG] 连接开始时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	// 配置 TLS
	tlsConfig := &tls.Config{
		RootCAs: nil,
	}

	// 设置 WebSocket 拨号器
	dialer := websocket.Dialer{
		TLSClientConfig: tlsConfig,
	}

	// 设置完整的请求头 - 使用随机浏览器配置
	headers := http.Header{
		"Origin":          []string{"https://matrix.tencent.com"},
		"Cache-Control":   []string{"no-cache"},
		"Accept-Language": []string{"zh-CN,zh;q=0.9"},
		"Pragma":          []string{"no-cache"},
		"User-Agent":      []string{client.browserConfig.UserAgent},
		"Cookie":          []string{client.browserConfig.Cookies},
	}

	fmt.Printf("   [DEBUG] 请求头设置完成，开始拨号...\n")
	connectStartTime := time.Now()

	// 建立连接
	conn, _, err := dialer.Dial(websocketURI, headers)
	if err != nil {
		fmt.Printf("   ❌ [ERROR] WebSocket连接失败: %v\n", err)
		fmt.Printf("   [ERROR] 连接耗时: %v\n", time.Since(connectStartTime))
		return fmt.Errorf("[STEP-1] WebSocket连接失败: %v", err)
	}

	client.conn = conn
	client.isConnected = true
	fmt.Printf("✅ [STEP-1] WebSocket连接成功建立\n")
	fmt.Printf("   [INFO] 连接耗时: %v\n", time.Since(connectStartTime))
	fmt.Printf("   [INFO] 连接状态: %t\n", client.isConnected)
	return nil
}

// InitializeAuth 第二步：初始化验证（发送指纹）
func (client *TextAnalysisClient) InitializeAuth() error {
	if !client.isConnected {
		fmt.Printf("   ❌ [ERROR] WebSocket未连接，无法初始化验证\n")
		return fmt.Errorf("[STEP-2] WebSocket未连接")
	}

	fmt.Println("🔧 [STEP-2] 开始初始化验证...")
	fmt.Printf("   [INFO] 指纹: %s\n", client.browserConfig.Fingerprint)
	fmt.Printf("   [DEBUG] 初始化开始时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	// 发送指纹信息
	fpPayload, _ := json.Marshal(map[string]string{"fp": client.browserConfig.Fingerprint})
	fpPayloadStr := string(fpPayload)
	fmt.Printf("   [DEBUG] 准备发送指纹数据: %s\n", fpPayload)

	sendStartTime := time.Now()
	err := client.conn.WriteMessage(websocket.TextMessage, fpPayload)
	if err != nil {
		fmt.Printf("   ❌ [ERROR] 发送指纹失败: %v\n", err)
		return fmt.Errorf("[STEP-2] 发送指纹失败: %v", err)
	}

	// 记录发送的指纹消息
	client.addSessionLog("SEND", fpPayloadStr, "fingerprint")
	fmt.Printf("   ⬆️  [SEND] 指纹发送成功，耗时: %v\n", time.Since(sendStartTime))

	// 接收状态响应
	fmt.Printf("   [DEBUG] 等待服务器响应...\n")
	readStartTime := time.Now()
	_, message, err := client.conn.ReadMessage()
	if err != nil {
		fmt.Printf("   ❌ [ERROR] 读取状态响应失败: %v\n", err)
		return fmt.Errorf("[STEP-2] 读取状态失败: %v", err)
	}

	// 记录接收的状态响应
	messageStr := string(message)
	client.addSessionLog("RECV", messageStr, "status")
	fmt.Printf("   ⬇️  [RECV] 收到状态响应，耗时: %v\n", time.Since(readStartTime))
	fmt.Printf("   [INFO] 服务器响应: %s\n", message)
	fmt.Printf("✅ [STEP-2] 初始化验证完成\n")
	return nil
}

// GetCredentials 第三步：获取凭证（重试直到获得低风险等级）
func (client *TextAnalysisClient) GetCredentials() error {
	fmt.Println("🔑 [STEP-3] 开始获取验证凭证...")
	fmt.Printf("   [DEBUG] 当前时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	maxRetries := 5 // 最多重试5次
	for attempt := 1; attempt <= maxRetries; attempt++ {
		fmt.Printf("   [ATTEMPT-%d] 第%d次尝试获取凭证...\n", attempt, attempt)

		credentials, err := getCaptchaCredentials(client.browserConfig)
		if err != nil {
			fmt.Printf("   ❌ [ERROR] 第%d次获取失败: %v\n", attempt, err)
			if attempt == maxRetries {
				fmt.Printf("❌ [ERROR] 获取验证码凭证失败\n")
				fmt.Printf("   [ERROR] 错误详情: %v\n", err)
				fmt.Printf("   [ERROR] 错误类型: %T\n", err)
				fmt.Printf("   [DEBUG] 当前连接状态: %t\n", client.isConnected)
				fmt.Printf("   [DEBUG] 当前凭证状态: %v\n", client.credentials != nil)
				return fmt.Errorf("[STEP-3] 获取验证码凭证失败: %v", err)
			}
			// 等待一段时间后重试
			fmt.Printf("   [INFO] 等待2秒后重试...\n")
			time.Sleep(2 * time.Second)
			continue
		}

		client.credentials = credentials

		// 安全地显示ticket信息
		ticketDisplay := credentials.Ticket
		if len(credentials.Ticket) > 20 {
			ticketDisplay = credentials.Ticket[:20] + "..."
		}
		fmt.Printf("   ✅ [ATTEMPT-%d] 获取凭证成功\n", attempt)
		fmt.Printf("   [INFO] Ticket: %s\n", ticketDisplay)
		fmt.Printf("   [INFO] Randstr: %s\n", credentials.Randstr)
		fmt.Printf("   [INFO] Ticket长度: %d字符\n", len(credentials.Ticket))

		// 发送凭证并检查响应
		fmt.Printf("   [CHECK] 发送凭证并检查服务器响应...\n")
		evilLevel, err := client.checkCredentialsRisk()
		if err != nil {
			fmt.Printf("   ❌ [ERROR] 发送凭证失败: %v\n", err)
			if attempt == maxRetries {
				return fmt.Errorf("[STEP-3] 发送凭证失败: %v", err)
			}
			continue
		}

		fmt.Printf("   [INFO] 服务器响应 - 风险等级: %s\n", evilLevel)

		// 获得任何响应都表示凭证有效，直接使用
		fmt.Printf("✅ [STEP-3] 凭证验证成功 (evil_level=%s)\n", evilLevel)
		fmt.Printf("   [INFO] 凭证已通过服务器验证，准备发送文本分析请求\n")
		fmt.Printf("   [DEBUG] 凭证获取完成时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
		return nil
	}

	// 所有重试都失败，使用最后一次的凭证
	fmt.Printf("⚠️  [WARN] 无法获取低风险凭证，使用最后一次的凭证继续\n")
	fmt.Printf("   [DEBUG] 凭证获取完成时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	return nil
}

// checkCredentialsRisk 检查凭证风险等级
func (client *TextAnalysisClient) checkCredentialsRisk() (string, error) {
	if !client.isConnected {
		return "", fmt.Errorf("WebSocket未连接")
	}
	if client.credentials == nil {
		return "", fmt.Errorf("凭证未获取")
	}

	// 发送人机验证凭证
	ticketPayload, _ := json.Marshal(map[string]string{
		"ticket":  client.credentials.Ticket,
		"randstr": client.credentials.Randstr,
	})

	ticketPayloadStr := string(ticketPayload)
	sendStartTime := time.Now()
	err := client.conn.WriteMessage(websocket.TextMessage, ticketPayload)
	if err != nil {
		return "", fmt.Errorf("发送凭证失败: %v", err)
	}

	// 记录发送的凭证消息
	client.addSessionLog("SEND", ticketPayloadStr, "credentials")
	fmt.Printf("   ⬆️  [SEND] 凭证发送成功，耗时: %v\n", time.Since(sendStartTime))

	// 接收验证结果
	readStartTime := time.Now()
	_, message, err := client.conn.ReadMessage()
	if err != nil {
		return "", fmt.Errorf("读取验证结果失败: %v", err)
	}

	// 记录接收的验证结果
	messageStr := string(message)
	client.addSessionLog("RECV", messageStr, "verification")
	fmt.Printf("   ⬇️  [RECV] 收到验证结果，耗时: %v\n", time.Since(readStartTime))
	fmt.Printf("   [INFO] 验证结果: %s\n", message)

	// 解析验证结果获取evil_level
	var result map[string]interface{}
	if err := json.Unmarshal(message, &result); err != nil {
		return "", fmt.Errorf("解析验证结果失败: %v", err)
	}

	if code, ok := result["code"].(string); ok && code == "1" {
		if evilLevel, ok := result["evil_level"].(string); ok {
			return evilLevel, nil
		}
	}

	return "", fmt.Errorf("无法获取风险等级")
}

// SendCredentials 发送凭证到服务器（简化版，因为风险检查已在GetCredentials中完成）
func (client *TextAnalysisClient) SendCredentials() error {
	fmt.Printf("   [INFO] 凭证已在获取阶段验证完成，跳过重复发送\n")
	fmt.Printf("   ✅ [STEP-3.6] 凭证验证完成\n")
	return nil
}

var yourText = `In reply to: personal accountability by pdundas
Afterall they can say: "Look, the patent-office, staffed by competent people that aren't us, examined the patent and granted it.
It's really tricky to argue that the patent-office saw the patent as valid, yet that anyone else who *also* sees it as valid, are acting with criminal neglect and/or malevolence.
(Log in to post comments)
How much are the (US) patent office expected to know about the current state of the art? Given the number of applications relative to the number of examiners, they would have to be sufficiently familiar with every field they deal with to have an idea off the top of their head about whether or not it is original if they are to spot prior art without help. Is this really what courts assume about them?
They'd have to be, afterall, because the chief criteria are first that it's novel, and second that it's non-obvious. The definition of the latter vary somewhat by jurisdiction, but for USA it means that:
"the difference between the subject matter sought to be patented and the prior art are such that the subject matter as a whole would have been obvious to a person having ordinary skill in the art to which the subject matter pertains."
In other words, if the technique being patented would have been obvious to a person who has knowledge of formerly used techniques *and* ordinary skill in the subject-area, then that technique is not patentable.
That's what the law says - offcourse in practice it doesn't work like that. It *is* obvious to a person of ordinary skill in developing payment-solutions for webshops that shopping can be made with one click if the required information has been stored previously - and it's equally obvious how to implement this. Nevertheless the patent was granted.
A patent-examiner, in principle, has examined the application and determined that it is novel, non-obvious and useful. It's gonna be really tricky to make it stick that believing his conclusions amounts to criminal neglect.
If you *could* make it stick: "Nobody in their right mind could believe this", then you could conceivably also sue the patent-office for criminal neglect, since they'd be guilty of the same mistake.
That'd be the day -- but I ain't holding my breath.`

// SendTextAndGetResult 第四步：发送文本并获取返回值
func (client *TextAnalysisClient) SendTextAndGetResult(text string) error {
	if !client.isConnected {
		fmt.Printf("   ❌ [ERROR] WebSocket未连接，无法发送文本\n")
		return fmt.Errorf("[STEP-4] WebSocket未连接")
	}

	fmt.Println("📝 [STEP-4] 开始发送文本并获取返回值...")
	fmt.Printf("   [INFO] 文本长度: %d字符\n", len(text))
	fmt.Printf("   [DEBUG] 文本预览: %s...\n", text[:min(100, len(text))])

	// 发送文本内容
	textPayload, _ := json.Marshal(map[string]string{"text": text})
	textPayloadStr := string(textPayload)
	fmt.Printf("   [DEBUG] 文本JSON长度: %d字节\n", len(textPayload))

	sendStartTime := time.Now()
	err := client.conn.WriteMessage(websocket.TextMessage, textPayload)
	if err != nil {
		fmt.Printf("   ❌ [ERROR] 发送文本失败: %v\n", err)
		return fmt.Errorf("[STEP-4] 发送文本失败: %v", err)
	}

	// 记录发送的文本消息
	client.addSessionLog("SEND", textPayloadStr, "text")
	fmt.Printf("   ⬆️  [SEND] 文本发送成功，耗时: %v\n", time.Since(sendStartTime))

	fmt.Printf("✅ [STEP-4] 数据已全部发送，等待服务器处理结果...\n")
	messageCount := 0
	analysisStartTime := time.Now()

	// 循环接收服务器返回的消息
	for {
		_, message, err := client.conn.ReadMessage()
		if err != nil {
			if websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
				fmt.Printf("   ❌ [ERROR] 连接被服务器关闭: %v\n", err)
				fmt.Printf("   [INFO] 总共收到 %d 条消息，分析耗时: %v\n", messageCount, time.Since(analysisStartTime))
				return fmt.Errorf("[STEP-4] 连接被关闭: %v", err)
			} else {
				fmt.Printf("   ❌ [ERROR] 读取消息失败: %v\n", err)
				return fmt.Errorf("[STEP-4] 读取消息失败: %v", err)
			}
		}

		messageCount++
		messageStr := string(message)
		fmt.Printf("   ⬇️  [RECV-%d] 收到消息 (%d字节): %s\n", messageCount, len(message), message)

		// 记录接收的消息
		client.addSessionLog("RECV", messageStr, "response")

		// 解析消息判断消息类型和状态
		var result map[string]interface{}
		if json.Unmarshal(message, &result) == nil {
			if status, ok := result["status"].(string); ok {
				switch status {
				case "waiting":
					// 处理等待状态
					remaining := 0
					if r, ok := result["remaining"].(float64); ok {
						remaining = int(r)
					}
					fmt.Printf("   [INFO] 服务器处理中，剩余: %d\n", remaining)
					continue

				case "success":
					// 检查是否为最终结果（包含完整分析数据）
					if labelsRatio, hasLabels := result["labels_ratio"]; hasLabels {
						if confidence, hasConf := result["confidence"]; hasConf {
							fmt.Printf("🎉 [STEP-4] 文本分析完成！\n")
							fmt.Printf("   [RESULT] 置信度: %v\n", confidence)
							fmt.Printf("   [RESULT] 标签比例: %v\n", labelsRatio)

							// 显示可用次数
							if availableUses, hasUses := result["availableUses"]; hasUses {
								fmt.Printf("   [INFO] 剩余可用次数: %v\n", availableUses)
							}

							fmt.Printf("   [INFO] 总共收到 %d 条消息\n", messageCount)
							fmt.Printf("   [INFO] 分析总耗时: %v\n", time.Since(analysisStartTime))
							return nil
						}
					}
					// 如果是success但没有完整结果，继续等待
					fmt.Printf("   [INFO] 收到成功状态，但结果不完整，继续等待...\n")
					continue

				default:
					fmt.Printf("   [INFO] 收到状态: %s\n", status)
					continue
				}
			} else {
				// 非状态消息，可能是其他类型的响应
				fmt.Printf("   [DEBUG] 收到非状态消息，继续等待...\n")
				continue
			}
		} else {
			fmt.Printf("   [WARN] 无法解析JSON消息，继续等待...\n")
			continue
		}
	}
}

// ProcessText 完整的文本处理流程（每次发送文本前都要获取凭证）
func (client *TextAnalysisClient) ProcessText(text string) error {
	fmt.Printf("\n🔄 [PROCESS] 开始处理文本分析...\n")
	fmt.Printf("   [INFO] 处理开始时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	processStartTime := time.Now()

	// 第三步：获取凭证（每次发送文本前都要获取）
	err := client.GetCredentials()
	if err != nil {
		fmt.Printf("   ❌ [PROCESS] 获取凭证失败: %v\n", err)
		return fmt.Errorf("[PROCESS] 获取凭证失败: %v", err)
	}

	// 发送凭证到服务器
	err = client.SendCredentials()
	if err != nil {
		fmt.Printf("   ❌ [PROCESS] 发送凭证失败: %v\n", err)
		return fmt.Errorf("[PROCESS] 发送凭证失败: %v", err)
	}

	// 第四步：发送文本并获取返回值
	err = client.SendTextAndGetResult(text)
	if err != nil {
		fmt.Printf("   ❌ [PROCESS] 发送文本失败: %v\n", err)
		return fmt.Errorf("[PROCESS] 发送文本失败: %v", err)
	}

	fmt.Printf("✅ [PROCESS] 文本处理流程完成\n")
	fmt.Printf("   [INFO] 总处理耗时: %v\n", time.Since(processStartTime))

	// 打印完整会话日志
	client.printSessionLog()
	return nil
}

// Close 关闭连接
func (client *TextAnalysisClient) Close() {
	if client.conn != nil {
		fmt.Printf("🔌 [CLOSE] 正在关闭WebSocket连接...\n")
		closeStartTime := time.Now()
		client.conn.Close()
		client.isConnected = false
		client.credentials = nil
		// 清理会话日志
		client.sessionLog = make([]SessionMessage, 0)
		fmt.Printf("✅ [CLOSE] WebSocket连接已关闭，耗时: %v\n", time.Since(closeStartTime))
		fmt.Printf("   [INFO] 连接状态: %t\n", client.isConnected)
	}
}

// IsConnected 检查连接状态
func (client *TextAnalysisClient) IsConnected() bool {
	return client.isConnected
}

// ============ 主程序 ============

func main() {
	fmt.Println("🚀 启动文本分析客户端...")
	fmt.Println("📋 程序流程：")
	fmt.Println("   1️⃣ 建立WebSocket连接")
	fmt.Println("   2️⃣ 初始化验证")
	fmt.Println("   3️⃣ 获取凭证")
	fmt.Println("   4️⃣ 发送文本并获取返回值")
	fmt.Println()

	// 创建客户端
	client := NewTextAnalysisClient()
	defer client.Close()

	for {
		// 第一步：建立WebSocket连接
		err := client.Connect()
		if err != nil {
			fmt.Printf("❌ 连接失败: %v，5秒后重试...\n", err)
			time.Sleep(5 * time.Second)
			continue
		}

		// 第二步：初始化验证
		err = client.InitializeAuth()
		if err != nil {
			fmt.Printf("❌ 初始化验证失败: %v\n", err)
			client.Close()
			time.Sleep(5 * time.Second)
			continue
		}

		// 处理文本分析（包含第三步和第四步）
		err = client.ProcessText(yourText)
		if err != nil {
			fmt.Printf("❌ 处理失败: %v\n", err)
			// 即使失败也打印会话日志用于调试
			client.printSessionLog()
		}

		// 关闭连接
		client.Close()

		fmt.Println("\n⏰ 等待10秒后进行下一次分析...")
		time.Sleep(10 * time.Second)

		// 询问用户是否继续
		fmt.Print("\n是否继续下一次分析？(y/n): ")
		reader := bufio.NewReader(os.Stdin)
		input, _ := reader.ReadString('\n')
		input = strings.TrimSpace(strings.ToLower(input))

		if input == "n" || input == "no" {
			fmt.Println("👋 程序结束")
			break
		}
	}
}
